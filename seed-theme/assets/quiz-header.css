/* Hide standard header on quiz page */
.quiz-page .shopify-section-header {
  display: none !important;
}

/* DEBUG: Add visible indicator when quiz page is detected */
.quiz-page::before {
  content: "QUIZ PAGE DETECTED - CUSTOM HEADER SHOULD BE VISIBLE";
  display: block;
  background: #ff0000;
  color: white;
  padding: 10px;
  text-align: center;
  font-weight: bold;
}

/* Quiz Header Styles */
.quiz-header {
  border-bottom: 1px solid var(--quiz-header-border-color, rgb(241, 241, 242));
  box-sizing: border-box;
  color: var(--quiz-header-text-color, rgb(74, 74, 74));
  display: block;
  font-family: var(--quiz-header-font-family, Lato, sans-serif);
  font-size: 16px;
  height: var(--quiz-header-height, 82.5625px);
  position: relative;
  width: 100%;
  background: #fff;
  z-index: 1000;
}

.quiz-header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

/* Left Logo Section */
.quiz-header-left {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.quiz-logo-link {
  display: inline-block;
  text-decoration: none;
  line-height: 0;
}

.quiz-logo-link svg {
  max-width: var(--quiz-logo-max-width, 125px);
  height: auto;
  display: block;
}

/* Center Image Section */
.quiz-header-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.quiz-center-image {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
  box-sizing: border-box;
  display: block;
  height: 80px;
  width: 80px;
  border-radius: 50%;
  opacity: 1;
  transition: all 0.2s linear;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* Right Link Section */
.quiz-header-right {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.quiz-right-link {
  background-color: rgba(0, 0, 0, 0);
  box-sizing: border-box;
  color: var(--quiz-header-link-color, rgb(105, 105, 105));
  cursor: pointer;
  display: block;
  font-family: var(--quiz-header-font-family, Lato, sans-serif);
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.0311111px;
  line-height: 22.4px;
  outline: none;
  position: relative;
  text-decoration: none;
  vertical-align: top;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  transition: color 0.2s ease;
}

.quiz-right-link:hover {
  color: var(--quiz-header-text-color, rgb(74, 74, 74));
}

/* Mobile Responsive Styles */
@media screen and (max-width: 768px) {
  .quiz-header {
    height: auto;
    min-height: 60px;
    padding: 10px 0;
  }

  .quiz-header-container {
    padding: 0 15px;
    flex-wrap: wrap;
    gap: 10px;
  }

  .quiz-logo-link svg {
    max-width: 100px;
  }

  .quiz-center-image {
    height: 60px;
    width: 60px;
  }

  .quiz-right-link {
    font-size: 14px;
    font-weight: 500;
  }

  /* Stack elements vertically on very small screens */
  @media screen and (max-width: 480px) {
    .quiz-header-container {
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: 15px;
      padding: 15px;
    }

    .quiz-header-center {
      position: relative;
      left: auto;
      top: auto;
      transform: none;
      order: -1;
    }

    .quiz-header-left {
      order: 1;
    }

    .quiz-header-right {
      order: 2;
    }

    .quiz-center-image {
      height: 50px;
      width: 50px;
    }

    .quiz-logo-link svg {
      max-width: 80px;
    }
  }
}

/* Tablet Styles */
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .quiz-header-container {
    padding: 0 30px;
  }

  .quiz-logo-link svg {
    max-width: 110px;
  }

  .quiz-center-image {
    height: 70px;
    width: 70px;
  }
}

/* Large Desktop Styles */
@media screen and (min-width: 1200px) {
  .quiz-header-container {
    max-width: 1400px;
    padding: 0 40px;
  }
}

/* High DPI / Retina Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .quiz-center-image {
    background-size: cover;
  }
}

/* Focus and Accessibility */
.quiz-logo-link:focus,
.quiz-right-link:focus {
  outline: 2px solid var(--quiz-header-link-color, rgb(105, 105, 105));
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .quiz-header {
    border-bottom: 1px solid #000;
    background: #fff;
  }
  
  .quiz-center-image {
    display: none;
  }
}
